<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res"><file name="scanner_animation" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\anim\scanner_animation.xml" qualifiers="" type="anim"/><file name="bottom_nav_color" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\color\bottom_nav_color.xml" qualifiers="" type="color"/><file name="add_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\add_shape.xml" qualifiers="" type="drawable"/><file name="apple" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\apple.webp" qualifiers="" type="drawable"/><file name="bg_round_corners" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\bg_round_corners.xml" qualifiers="" type="drawable"/><file name="bg_round_corners_2" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\bg_round_corners_2.xml" qualifiers="" type="drawable"/><file name="bg_round_white" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\bg_round_white.xml" qualifiers="" type="drawable"/><file name="bg_stat_card" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\bg_stat_card.xml" qualifiers="" type="drawable"/><file name="bottom_sheet_background" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\bottom_sheet_background.xml" qualifiers="" type="drawable"/><file name="bottom_sheet_rounded_background" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\bottom_sheet_rounded_background.xml" qualifiers="" type="drawable"/><file name="chat_input_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\chat_input_shape.xml" qualifiers="" type="drawable"/><file name="check" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\check.png" qualifiers="" type="drawable"/><file name="checkbox_color_selector" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\checkbox_color_selector.xml" qualifiers="" type="drawable"/><file name="checked" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\checked.png" qualifiers="" type="drawable"/><file name="chevron_bottom" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\chevron_bottom.png" qualifiers="" type="drawable"/><file name="compney_health" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\compney_health.png" qualifiers="" type="drawable"/><file name="cross" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\cross.png" qualifiers="" type="drawable"/><file name="custom_checkbox_selector" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\custom_checkbox_selector.xml" qualifiers="" type="drawable"/><file name="delete" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\delete.png" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="edit" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\edit.png" qualifiers="" type="drawable"/><file name="filter_button_background" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\filter_button_background.xml" qualifiers="" type="drawable"/><file name="google" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\google.webp" qualifiers="" type="drawable"/><file name="green_check" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\green_check.png" qualifiers="" type="drawable"/><file name="icon_left" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\icon_left.png" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_add.png" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_arrow_right.png" qualifiers="" type="drawable"/><file name="ic_arrow_up_right" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_arrow_up_right.png" qualifiers="" type="drawable"/><file name="ic_baseline_add_24" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_baseline_add_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_camera_alt_24" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_baseline_camera_alt_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_close_24" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_baseline_close_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_image_24" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_baseline_image_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_play_circle_24" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_baseline_play_circle_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_send_24" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_baseline_send_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_video_library_24" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_baseline_video_library_24.xml" qualifiers="" type="drawable"/><file name="ic_cart" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_cart.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_default" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_default.xml" qualifiers="" type="drawable"/><file name="ic_delete_icon" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_delete_icon.png" qualifiers="" type="drawable"/><file name="ic_dropdown" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_dropdown.png" qualifiers="" type="drawable"/><file name="ic_edit_icon" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_edit_icon.png" qualifiers="" type="drawable"/><file name="ic_info_custom_fill" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_info_custom_fill.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_loc_active" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_loc_active.png" qualifiers="" type="drawable"/><file name="ic_menu" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_options_bold" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_options_bold.png" qualifiers="" type="drawable"/><file name="ic_option_light" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_option_light.png" qualifiers="" type="drawable"/><file name="ic_profile" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="image_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\image_shape.xml" qualifiers="" type="drawable"/><file name="info" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\info.png" qualifiers="" type="drawable"/><file name="logout_icon" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\logout_icon.png" qualifiers="" type="drawable"/><file name="plus_button" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\plus_button.png" qualifiers="" type="drawable"/><file name="profit" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\profit.png" qualifiers="" type="drawable"/><file name="profit_overhead" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\profit_overhead.png" qualifiers="" type="drawable"/><file name="progress_bar" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\progress_bar.xml" qualifiers="" type="drawable"/><file name="receive_message_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\receive_message_shape.xml" qualifiers="" type="drawable"/><file name="rounded_edittext" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\rounded_edittext.xml" qualifiers="" type="drawable"/><file name="rounded_edittext2" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\rounded_edittext2.xml" qualifiers="" type="drawable"/><file name="rounded_edittext2_none_editable" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\rounded_edittext2_none_editable.xml" qualifiers="" type="drawable"/><file name="rounded_edittext_none_editable" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\rounded_edittext_none_editable.xml" qualifiers="" type="drawable"/><file name="rounded_textview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\rounded_textview.xml" qualifiers="" type="drawable"/><file name="rounded_textview_bg" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\rounded_textview_bg.xml" qualifiers="" type="drawable"/><file name="select_camera_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\select_camera_shape.xml" qualifiers="" type="drawable"/><file name="select_image_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\select_image_shape.xml" qualifiers="" type="drawable"/><file name="select_video_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\select_video_shape.xml" qualifiers="" type="drawable"/><file name="send_button_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\send_button_shape.xml" qualifiers="" type="drawable"/><file name="send_message_shape" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\send_message_shape.xml" qualifiers="" type="drawable"/><file name="setting_icon" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\setting_icon.png" qualifiers="" type="drawable"/><file name="subscription_icon" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\subscription_icon.png" qualifiers="" type="drawable"/><file name="tab_account" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_account.xml" qualifiers="" type="drawable"/><file name="tab_account_selected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_account_selected.png" qualifiers="" type="drawable"/><file name="tab_account_unselected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_account_unselected.png" qualifiers="" type="drawable"/><file name="tab_cost" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_cost.xml" qualifiers="" type="drawable"/><file name="tab_cost_selected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_cost_selected.png" qualifiers="" type="drawable"/><file name="tab_cost_unselected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_cost_unselected.png" qualifiers="" type="drawable"/><file name="tab_home" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_home.xml" qualifiers="" type="drawable"/><file name="tab_home_selected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_home_selected.png" qualifiers="" type="drawable"/><file name="tab_home_unselected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_home_unselected.png" qualifiers="" type="drawable"/><file name="tab_project" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_project.xml" qualifiers="" type="drawable"/><file name="tab_project_selected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_project_selected.png" qualifiers="" type="drawable"/><file name="tab_project_unselected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_project_unselected.png" qualifiers="" type="drawable"/><file name="tab_team" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_team.xml" qualifiers="" type="drawable"/><file name="tab_team_selected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_team_selected.png" qualifiers="" type="drawable"/><file name="tab_team_unselected" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\tab_team_unselected.png" qualifiers="" type="drawable"/><file name="unchecked" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\unchecked.png" qualifiers="" type="drawable"/><file name="user_icon" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\user_icon.png" qualifiers="" type="drawable"/><file name="inter" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\inter.xml" qualifiers="" type="font"/><file name="inter_18pt_bold" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\inter_18pt_bold.ttf" qualifiers="" type="font"/><file name="inter_18pt_medium" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\inter_18pt_medium.ttf" qualifiers="" type="font"/><file name="inter_18pt_regular" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\inter_18pt_regular.ttf" qualifiers="" type="font"/><file name="inter_18pt_semibold" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\inter_18pt_semibold.ttf" qualifiers="" type="font"/><file name="medium" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\medium.ttf" qualifiers="" type="font"/><file name="semibold" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\semibold.ttf" qualifiers="" type="font"/><file name="sfpro" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\sfpro.xml" qualifiers="" type="font"/><file name="sf_pro" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\sf_pro.ttf" qualifiers="" type="font"/><file name="sf_pro_italic" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\font\sf_pro_italic.ttf" qualifiers="" type="font"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="bottom_add_draw" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_add_draw.xml" qualifiers="" type="layout"/><file name="bottom_add_employee" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_add_employee.xml" qualifiers="" type="layout"/><file name="bottom_add_lineal" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_add_lineal.xml" qualifiers="" type="layout"/><file name="bottom_add_material" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_add_material.xml" qualifiers="" type="layout"/><file name="bottom_edit_profile" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_edit_profile.xml" qualifiers="" type="layout"/><file name="bottom_select_customer" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_select_customer.xml" qualifiers="" type="layout"/><file name="bottom_sheet_month_filter" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_sheet_month_filter.xml" qualifiers="" type="layout"/><file name="bottom_sheet_multi_select_status_filter" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_sheet_multi_select_status_filter.xml" qualifiers="" type="layout"/><file name="bottom_sheet_status_filter" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_sheet_status_filter.xml" qualifiers="" type="layout"/><file name="bottom_update_password" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_update_password.xml" qualifiers="" type="layout"/><file name="bottom_update_profile" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\bottom_update_profile.xml" qualifiers="" type="layout"/><file name="dialog_add_lineal" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\dialog_add_lineal.xml" qualifiers="" type="layout"/><file name="dialog_add_material" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\dialog_add_material.xml" qualifiers="" type="layout"/><file name="dialog_add_square" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\dialog_add_square.xml" qualifiers="" type="layout"/><file name="dialog_alert_view" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\dialog_alert_view.xml" qualifiers="" type="layout"/><file name="dialog_forgetpassword" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\dialog_forgetpassword.xml" qualifiers="" type="layout"/><file name="dialog_resetpassword" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\dialog_resetpassword.xml" qualifiers="" type="layout"/><file name="fragment_accountview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_accountview.xml" qualifiers="" type="layout"/><file name="fragment_add_line_items" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_add_line_items.xml" qualifiers="" type="layout"/><file name="fragment_alerts" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_alerts.xml" qualifiers="" type="layout"/><file name="fragment_companysetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_companysetup.xml" qualifiers="" type="layout"/><file name="fragment_completesetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_completesetup.xml" qualifiers="" type="layout"/><file name="fragment_costview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_costview.xml" qualifiers="" type="layout"/><file name="fragment_create_customer" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_create_customer.xml" qualifiers="" type="layout"/><file name="fragment_create_estimation" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_create_estimation.xml" qualifiers="" type="layout"/><file name="fragment_dashboardview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_dashboardview.xml" qualifiers="" type="layout"/><file name="fragment_draws" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_draws.xml" qualifiers="" type="layout"/><file name="fragment_forget_password" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_forget_password.xml" qualifiers="" type="layout"/><file name="fragment_friend_list" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_friend_list.xml" qualifiers="" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_labortrackingview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_labortrackingview.xml" qualifiers="" type="layout"/><file name="fragment_linealsetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_linealsetup.xml" qualifiers="" type="layout"/><file name="fragment_linear_line_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_linear_line_item.xml" qualifiers="" type="layout"/><file name="fragment_line_items" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_line_items.xml" qualifiers="" type="layout"/><file name="fragment_login" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_login.xml" qualifiers="" type="layout"/><file name="fragment_materialsetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_materialsetup.xml" qualifiers="" type="layout"/><file name="fragment_material_line_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_material_line_item.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="fragment_profileview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_profileview.xml" qualifiers="" type="layout"/><file name="fragment_profile_edit" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_profile_edit.xml" qualifiers="" type="layout"/><file name="fragment_projectview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_projectview.xml" qualifiers="" type="layout"/><file name="fragment_reset_password" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_reset_password.xml" qualifiers="" type="layout"/><file name="fragment_room_list" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_room_list.xml" qualifiers="" type="layout"/><file name="fragment_sign_up" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_sign_up.xml" qualifiers="" type="layout"/><file name="fragment_splash" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_splash.xml" qualifiers="" type="layout"/><file name="fragment_squresetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_squresetup.xml" qualifiers="" type="layout"/><file name="fragment_subscription" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_subscription.xml" qualifiers="" type="layout"/><file name="fragment_trackingview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_trackingview.xml" qualifiers="" type="layout"/><file name="fragment_workerview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\fragment_workerview.xml" qualifiers="" type="layout"/><file name="header" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\header.xml" qualifiers="" type="layout"/><file name="item_app_alert" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_app_alert.xml" qualifiers="" type="layout"/><file name="item_broadcast_video" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_broadcast_video.xml" qualifiers="" type="layout"/><file name="item_customer" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_customer.xml" qualifiers="" type="layout"/><file name="item_dashboard_project" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_dashboard_project.xml" qualifiers="" type="layout"/><file name="item_draw" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_draw.xml" qualifiers="" type="layout"/><file name="item_employee" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_employee.xml" qualifiers="" type="layout"/><file name="item_filter_option" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_filter_option.xml" qualifiers="" type="layout"/><file name="item_line" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_line.xml" qualifiers="" type="layout"/><file name="item_lineal" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_lineal.xml" qualifiers="" type="layout"/><file name="item_line_lineal" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_line_lineal.xml" qualifiers="" type="layout"/><file name="item_line_material" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_line_material.xml" qualifiers="" type="layout"/><file name="item_line_total" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_line_total.xml" qualifiers="" type="layout"/><file name="item_material" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_material.xml" qualifiers="" type="layout"/><file name="item_multi_select_filter_option" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\item_multi_select_filter_option.xml" qualifiers="" type="layout"/><file name="progress_dialog" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\progress_dialog.xml" qualifiers="" type="layout"/><file name="receive_image_message_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\receive_image_message_item.xml" qualifiers="" type="layout"/><file name="receive_text_message_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\receive_text_message_item.xml" qualifiers="" type="layout"/><file name="receive_video_message_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\receive_video_message_item.xml" qualifiers="" type="layout"/><file name="room_data" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\room_data.xml" qualifiers="" type="layout"/><file name="send_image_message_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\send_image_message_item.xml" qualifiers="" type="layout"/><file name="send_text_message_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\send_text_message_item.xml" qualifiers="" type="layout"/><file name="send_video_message_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\send_video_message_item.xml" qualifiers="" type="layout"/><file name="simple_chat_view_widget" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\simple_chat_view_widget.xml" qualifiers="" type="layout"/><file name="user_data" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout\user_data.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\activity_main.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_add_draw" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_add_draw.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_add_employee" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_add_employee.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_add_lineal" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_add_lineal.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_add_material" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_add_material.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_edit_profile" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_edit_profile.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_select_customer" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_select_customer.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_sheet_month_filter" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_sheet_month_filter.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_sheet_multi_select_status_filter" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_sheet_multi_select_status_filter.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_sheet_status_filter" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_sheet_status_filter.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_update_password" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_update_password.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_update_profile" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\bottom_update_profile.xml" qualifiers="sw600dp-v13" type="layout"/><file name="dialog_add_lineal" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\dialog_add_lineal.xml" qualifiers="sw600dp-v13" type="layout"/><file name="dialog_add_material" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\dialog_add_material.xml" qualifiers="sw600dp-v13" type="layout"/><file name="dialog_add_square" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\dialog_add_square.xml" qualifiers="sw600dp-v13" type="layout"/><file name="dialog_forgetpassword" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\dialog_forgetpassword.xml" qualifiers="sw600dp-v13" type="layout"/><file name="dialog_resetpassword" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\dialog_resetpassword.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_accountview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_accountview.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_add_line_items" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_add_line_items.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_companysetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_companysetup.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_completesetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_completesetup.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_create_customer" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_create_customer.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_create_estimation" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_create_estimation.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_dashboardview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_dashboardview.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_draws" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_draws.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_forget_password" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_forget_password.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_home.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_linealsetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_linealsetup.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_linear_line_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_linear_line_item.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_line_items" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_line_items.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_login" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_login.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_materialsetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_materialsetup.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_material_line_item" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_material_line_item.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_profileview" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_profileview.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_reset_password" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_reset_password.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_sign_up" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_sign_up.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_squresetup" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_squresetup.xml" qualifiers="sw600dp-v13" type="layout"/><file name="fragment_subscription" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\fragment_subscription.xml" qualifiers="sw600dp-v13" type="layout"/><file name="header" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\header.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_customer" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_customer.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_draw" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_draw.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_employee" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_employee.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_line" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_line.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_lineal" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_lineal.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_line_lineal" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_line_lineal.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_line_material" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_line_material.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_line_total" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_line_total.xml" qualifiers="sw600dp-v13" type="layout"/><file name="item_material" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\layout-sw600dp\item_material.xml" qualifiers="sw600dp-v13" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="mobile_navigation" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\navigation\mobile_navigation.xml" qualifiers="" type="navigation"/><file name="loader" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\raw\loader.json" qualifiers="" type="raw"/><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\values\attrs.xml" qualifiers=""><attr format="color" name="colorCompany"/><declare-styleable name="SimpleChatView">
    <attr format="boolean" name="showAddButton"/>
    <attr format="color" name="addButtonColor"/>
    <attr format="color" name="chatViewBackgroundColor"/>
    <attr format="color" name="chatInputBackgroundColor"/>
    <attr format="integer" name="chatInputBackground"/>
    <attr format="color" name="hintTextColor"/>
    <attr format="color" name="textColor"/>
    <attr format="string" name="hint"/>
    <attr format="color" name="sendButtonColor"/>
    <attr format="boolean" name="showImageButton"/>
    <attr format="boolean" name="showVideoButton"/>
    <attr format="boolean" name="showCameraButton"/>
    <attr format="boolean" name="showSenderLayout"/>
</declare-styleable></file><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\values\colors.xml" qualifiers=""><color name="app_bg_color">#fdfdfd</color><color name="md_theme_light_primary">#000000</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="seed">#FF0000</color><color name="md_theme_light_primaryContainer">#FFDAD4</color><color name="md_theme_light_onPrimaryContainer">#410000</color><color name="md_theme_light_secondary">#775651</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#f5f5f5</color><color name="md_theme_light_onSecondaryContainer">#2C1512</color><color name="md_theme_light_tertiary">#9B404F</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#FFD9DC</color><color name="md_theme_light_onTertiaryContainer">#400010</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_background">#F2F2F7</color><color name="md_theme_light_onBackground">#001B3D</color><color name="md_theme_light_surface">#FDFBFF</color><color name="md_theme_light_onSurface">#001B3D</color><color name="md_theme_light_surfaceVariant">#F5DDDA</color><color name="md_theme_light_onSurfaceVariant">#534341</color><color name="md_theme_light_outline">#857370</color><color name="md_theme_light_inverseOnSurface">#ECF0FF</color><color name="md_theme_light_inverseSurface">#003062</color><color name="md_theme_light_inversePrimary">#FFB4A8</color><color name="md_theme_light_shadow">#000000</color><color name="md_theme_light_surfaceTint">#C00100</color><color name="md_theme_light_outlineVariant">#D8C2BE</color><color name="md_theme_light_scrim">#000000</color><color name="color_text_light">#000000</color><color name="color_text_dark">#FFFFFF</color><color name="md_theme_dark_primary">#FFB4A8</color><color name="md_theme_dark_onPrimary">#690100</color><color name="md_theme_dark_primaryContainer">#930100</color><color name="md_theme_dark_onPrimaryContainer">#FFDAD4</color><color name="md_theme_dark_secondary">#E7BDB6</color><color name="md_theme_dark_onSecondary">#442925</color><color name="md_theme_dark_secondaryContainer">#5D3F3B</color><color name="md_theme_dark_onSecondaryContainer">#FFDAD4</color><color name="md_theme_dark_tertiary">#FFB2BA</color><color name="md_theme_dark_onTertiary">#5F1223</color><color name="md_theme_dark_tertiaryContainer">#7D2938</color><color name="md_theme_dark_onTertiaryContainer">#FFD9DC</color><color name="md_theme_dark_error">#FFB4AB</color><color name="md_theme_dark_errorContainer">#93000A</color><color name="md_theme_dark_onError">#690005</color><color name="md_theme_dark_onErrorContainer">#FFDAD6</color><color name="md_theme_dark_background">#001B3D</color><color name="md_theme_dark_onBackground">#D6E3FF</color><color name="md_theme_dark_surface">#001B3D</color><color name="md_theme_dark_onSurface">#D6E3FF</color><color name="md_theme_dark_surfaceVariant">#534341</color><color name="md_theme_dark_onSurfaceVariant">#D8C2BE</color><color name="md_theme_dark_outline">#A08C89</color><color name="md_theme_dark_inverseOnSurface">#001B3D</color><color name="md_theme_dark_inverseSurface">#D6E3FF</color><color name="md_theme_dark_inversePrimary">#C00100</color><color name="md_theme_dark_shadow">#000000</color><color name="md_theme_dark_surfaceTint">#FFB4A8</color><color name="md_theme_dark_outlineVariant">#534341</color><color name="md_theme_dark_scrim">#000000</color><color name="semired">#F6E5EB</color><color name="gray">#8181A4</color><color name="semigray">#DEDDFC</color><color name="green">#51AE76</color><color name="semigreen">#DDFFE3</color><color name="blue">#50A8F9</color><color name="semiblue">#E0EBF8</color><color name="redgradstart">#F95050</color><color name="redgradend">#FFB1B1</color><color name="white">#FFFFFF</color><color name="white70">#f3f3f3</color><color name="yellow">#EBC308</color><color name="white90">#f9f9f9</color><color name="black90">#ff000000</color><color name="black">#000000</color><color name="bgGray">#E1E1EB</color><color name="greenStepDone">#2A7A7B</color><color name="notableSaleInstructionsBackgroundColor">#fafbfb</color><color name="profit_black">#0A0D14</color><color name="profit_brown">#450A0A</color><color name="profit_grey">#868C98</color><color name="profit_blue">#375DFB</color><color name="project_blue_bg">#C2D6FF</color><color name="project_green_bg">#CBF5E5</color><color name="project_purple_bg">#CAC2FF</color><color name="project_blue">#162664</color><color name="project_green">#176448</color><color name="project_purple">#2B1664</color><color name="project_brown">#6E330C</color><color name="red_500">#F44336</color><color name="project_orange">#FFDAC2</color><color name="project_yellow">#FBDFB1</color><color name="project_dark_yellow">#693D11</color><color name="text_sub">#525866</color><color name="stroke_soft">#E2E4E9</color><color name="stroke_light">#F5F5F5</color><color name="light_gray_bg">#F6F8FA</color><color name="brand_green">#60e73e</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="fragment_horizontal_margin">16dp</dimen><dimen name="fragment_vertical_margin">16dp</dimen><dimen name="tablet_dialog_width">600dp</dimen><dimen name="tablet_dialog_content_width">500dp</dimen><dimen name="tablet_dialog_margin">16dp</dimen><dimen name="tablet_dialog_corner_radius">8dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\values\ids.xml" qualifiers=""><item name="btnCancelProject" type="id"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">ProfitPro</string><string name="nav_open">Open</string><string name="nav_close">Close</string><string name="something_went_wrong">Something went wrong</string><string name="welcome">Welcome</string><string name="email">Email</string><string name="invalid_email">Invalid Email</string><string name="invalid_password">Invalid Password</string><string name="forget_password"><u>Forget Password</u></string><string name="invalid_email_password">Invalid Email Or Password</string><string name="name">Name</string><string name="username">Username</string><string name="password">Password</string><string name="confirm_password">Confirm Password</string><string name="password_does_not_match">Password does not match</string><string name="i_agree_to_user_policy_terms">I agree to Terms of Service</string><string name="login">Login</string><string name="submit">Submit</string><string name="sign_in">Sign in</string><string name="create_account">Create Account</string><string name="already_have_an_account">Already have an account?</string><string name="auth_sign_up">Sign Up</string><string name="register">Register</string><string name="sign_up">Sign Up</string><string name="don_t_have_an_account">"Don't have an account? <u><b>Sign Up</b></u>"</string><string name="already_have_account">"Already have account? <u><b>Sign In</b></u>"</string><string name="back">"Back"</string><string name="empty_location">Empty Location not allowed</string><string name="try_again">Try again</string><string name="google">Continue with Google</string><string name="apple">Continue with Apple</string><string name="add_button">Add button</string><string name="type_message">Type message</string><string name="send_button">send button</string><string name="send_image">send image</string><string name="image">Image</string><string name="send_video">Send video</string><string name="open_camera">Open camera</string><string name="date">Date</string><string name="open_dialog">Open Dialog</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\values\style.xml" qualifiers=""><style name="BottomNavTextAppearance" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">11sp</item>
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:fontWeight">600</item>
    </style><style name="ResponsiveDialogTheme" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\values\themes.xml" qualifiers=""><style name="AppTheme" parent="Theme.Material3.Light.NoActionBar">
      <item name="colorPrimary">@color/md_theme_light_primary</item>
      <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
      <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
      <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
      <item name="colorSecondary">@color/md_theme_light_secondary</item>
      <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
      <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
      <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
      <item name="colorTertiary">@color/md_theme_light_tertiary</item>
      <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
      <item name="colorControlActivated">@color/black</item>
      <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
      <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
      <item name="colorError">@color/md_theme_light_error</item>
      <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
      <item name="colorOnError">@color/md_theme_light_onError</item>
      <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
      <item name="android:colorBackground">@color/md_theme_light_background</item>
      <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
      <item name="colorSurface">@color/md_theme_light_surface</item>
      <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
      <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
      <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
      <item name="colorOutline">@color/md_theme_light_outline</item>
      <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
      <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
      <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>
      <item name="android:fontFamily">@font/medium</item>
      <item name="android:textColor">@color/color_text_light</item>
  </style><style name="h1" parent="@android:style/Widget.TextView">
      <item name="android:layout_width">wrap_content</item>
      <item name="android:layout_height">wrap_content</item>
      <item name="android:textSize">30sp</item>
      <item name="android:fontFamily">@font/semibold</item>
  </style><style name="ActionBarThemeOverlay">
        <item name="android:textColorPrimary">@color/white</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="colorControlHighlight">@color/white</item>
        <item name="android:textColorHint">@color/white</item>
    </style><style name="h2" parent="@android:style/Widget.TextView">
      <item name="android:layout_width">wrap_content</item>
      <item name="android:layout_height">wrap_content</item>
      <item name="android:textSize">24sp</item>
      <item name="android:fontFamily">@font/semibold</item>
  </style><style name="h3" parent="@android:style/Widget.TextView">
      <item name="android:layout_width">wrap_content</item>
      <item name="android:layout_height">wrap_content</item>
      <item name="android:textSize">18sp</item>
      <item name="android:fontFamily">@font/medium</item>
  </style><style name="subtitle" parent="@android:style/Widget.TextView">
      <item name="android:layout_width">wrap_content</item>
      <item name="android:layout_height">wrap_content</item>
      <item name="android:textSize">16sp</item>
      <item name="android:fontFamily">@font/semibold</item>
  </style><style name="text" parent="@android:style/Widget.TextView">
      <item name="android:layout_width">wrap_content</item>
      <item name="android:layout_height">wrap_content</item>
      <item name="android:textSize">14sp</item>
      <item name="android:fontFamily">@font/medium</item>
  </style><style name="small" parent="@android:style/Widget.TextView">
      <item name="android:layout_width">wrap_content</item>
      <item name="android:layout_height">wrap_content</item>
      <item name="android:textSize">12sp</item>
      <item name="android:fontFamily">@font/medium</item>
  </style><style name="CustomActiveIndicatorStyle">
      <item name="shapeAppearanceOverlay">@style/CustomActiveIndicatorShape</item>
  </style><style name="CustomActiveIndicatorShape">
      <item name="background">@null</item>
  </style><style name="BottomSheetDialogTheme" parent="Theme.MaterialComponents.Light.BottomSheetDialog">
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>

    </style><style name="CustomBottomSheetStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="shapeAppearance">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="backgroundTint">@android:color/transparent</item>
    </style><style name="CustomShapeAppearance" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">32dp</item>
        <item name="cornerSizeTopRight">32dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_provider" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\xml\file_provider.xml" qualifiers="" type="xml"/><file name="chevron_up" path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\res\drawable\chevron_up.png" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\generated\res\google-services\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\generated\crashlytics\res\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\generated\res\google-services\debug"><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\generated\res\google-services\debug\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">356934742115-bntd5isbkf9q9an3vejknu14khmp6nm7.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">356934742115</string><string name="google_api_key" translatable="false">AIzaSyBVQhaqkl3s4QAIwPQKBKzZAWy1Z61dQqg</string><string name="google_app_id" translatable="false">1:356934742115:android:df7bda1cc1dce7abae4856</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBVQhaqkl3s4QAIwPQKBKzZAWy1Z61dQqg</string><string name="google_storage_bucket" translatable="false">baasprod-933af.appspot.com</string><string name="project_id" translatable="false">baasprod-933af</string></file></source><source path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\generated\crashlytics\res\debug"><file path="C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\build\generated\crashlytics\res\debug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="SimpleChatView">
    <attr format="boolean" name="showAddButton"/>
    <attr format="color" name="addButtonColor"/>
    <attr format="color" name="chatViewBackgroundColor"/>
    <attr format="color" name="chatInputBackgroundColor"/>
    <attr format="integer" name="chatInputBackground"/>
    <attr format="color" name="hintTextColor"/>
    <attr format="color" name="textColor"/>
    <attr format="string" name="hint"/>
    <attr format="color" name="sendButtonColor"/>
    <attr format="boolean" name="showImageButton"/>
    <attr format="boolean" name="showVideoButton"/>
    <attr format="boolean" name="showCameraButton"/>
    <attr format="boolean" name="showSenderLayout"/>
</declare-styleable></configuration></mergedItems></merger>