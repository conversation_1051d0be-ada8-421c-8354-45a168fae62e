package com.manaknight.app.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import Manaknight.R
import androidx.compose.foundation.layout.padding

/**
 * Custom checkbox with brand green background and black checkmark
 */
@Composable
fun CustomCheckbox(
    checked: <PERSON><PERSON><PERSON>,
    onCheckedChange: ((<PERSON>olean) -> Unit)?,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val brandGreen = colorResource(R.color.brand_green)
    val uncheckedColor = if (enabled) Color.Gray else Color.LightGray
    val checkmarkColor = if (enabled) Color.Black else Color.Gray
    val backgroundColor = when {
        checked && enabled -> brandGreen
        checked && !enabled -> Color.LightGray
        else -> Color.Transparent
    }
    val borderColor = when {
        checked && enabled -> brandGreen
        checked && !enabled -> Color.LightGray
        enabled -> uncheckedColor
        else -> Color.LightGray
    }

    Box(
        modifier = modifier
            .padding(horizontal = 8.dp) // Always apply left margin
            .then(modifier) // Allow user to still pass additional modifiers
            .size(24.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(
                color = backgroundColor
            )
            .border(
                width = 2.dp,
                color = borderColor,
                shape = RoundedCornerShape(4.dp)
            )
            .clickable(enabled = enabled) {
                onCheckedChange?.invoke(!checked)
            },
        contentAlignment = Alignment.Center
    ) {
        if (checked) {
            Canvas(
                modifier = Modifier.size(14.dp)
            ) {
                drawCheckmark(checkmarkColor)
            }
        }
    }
}

/**
 * Draws a checkmark (tick) symbol
 */
private fun DrawScope.drawCheckmark(color: Color) {
    val path = Path().apply {
        // Start point (left side of checkmark)
        moveTo(size.width * 0.2f, size.height * 0.5f)
        // Middle point (bottom of checkmark)
        lineTo(size.width * 0.45f, size.height * 0.75f)
        // End point (right side of checkmark)
        lineTo(size.width * 0.8f, size.height * 0.25f)
    }

    drawPath(
        path = path,
        color = color,
        style = androidx.compose.ui.graphics.drawscope.Stroke(
            width = 2.5f,
            cap = StrokeCap.Round
        )
    )
}
